# UNNEST and Projection Pushdown Redesign

## Problem Discovery

### Initial UNNEST Issue
During testing, UNNEST operations were returning None values instead of actual data:
```sql
SELECT unnest(numbers) FROM streaming_json_reader('data.json')
-- Expected: [(1,), (2,), (3,), (4,), (5,), (6,), (7,)]
-- Actual: [(None,), (None,), (None,), (None,), (None,), (None,), (None,)]
```

### Investigation Findings

1. **Basic Array Access Works**: `SELECT numbers FROM ...` returns correct data
2. **Array Element Access Works**: `SELECT numbers[1] FROM ...` returns correct data  
3. **UNNEST Structure Recognition**: UNNEST returns correct number of elements but all None values
4. **Projection Pushdown Interference**: Extension wasn't mapping schema indices to output vector indices

### Root Cause Analysis

The investigation revealed two fundamental architectural problems:

#### 1. Projection Pushdown Mapping Issue
- Schema has fields at indices [0, 1, 2] but projected query only requests field 1
- DuckDB creates output with 1 vector (index 0) for projected column
- <PERSON> tried to access `output.list_vector(1)` which doesn't exist
- **Fixed**: Implemented projection mapping from schema indices to output vector indices

#### 2. Missing DuckDB List Vector Requirements
- DuckDB requires `list_vector.set_len(total_elements)` for UNNEST to work
- Extension was setting list entries but not vector lengths
- **Partially Fixed**: Added `set_len()` calls for primitive and nested arrays

#### 3. Over-Engineered Offset System
- Current implementation uses global cumulative offset tracking
- This is **not required by DuckDB** - it's an artifact of this codebase
- DuckDB's native list vectors manage offsets independently
- Creates unnecessary complexity and conflicts with projection pushdown

## Current Architecture Problems

### Global Offset Tracking System
```rust
struct CumulativeOffsetTracker {
    current_offsets: HashMap<VectorPath, usize>,
    capacity_limits: HashMap<VectorPath, usize>,
    allocation_history: Vec<AllocationRecord>,
}
```

**Problems:**
- Assumes sequential processing of all data
- Requires pre-calculation of vector capacities from full schema
- Cannot handle projection pushdown at nested levels
- Over-complicates simple list vector operations

### Projection Pushdown Limitations
Current projection only works at top level:
```rust
// Only handles: SELECT field1, field2 FROM ...
// Cannot handle: SELECT obj.nested.array[].field FROM ...
```

**Missing:**
- Path-based projection at all nesting levels
- Ability to skip entire JSON subtrees not in projection
- Dynamic vector creation for projected paths only

### Memory Inefficiency
- Pre-allocates vectors for entire document schema
- Processes all JSON data even if not projected
- Cannot achieve true streaming with minimal memory footprint

## Correct Design

### 1. Native DuckDB List Vector Management

**Replace global offset tracking with DuckDB's native approach:**
- Each list vector manages its own child vector independently
- Use standard `list_vector.set_entry(row, offset, length)` patterns
- Call `list_vector.set_len(total_child_elements)` for each level
- No global offset coordination needed

### 2. Path-Based Projection System

**Implement projection at all nesting levels:**
```rust
struct ProjectionPath {
    segments: Vec<PathSegment>,
}

enum PathSegment {
    Field(String),      // obj.field
    ArrayIndex(usize),  // array[0]
    ArrayAll,           // array[]
}
```

**Benefits:**
- Skip entire JSON subtrees not in projection
- Create vectors only for projected paths
- True streaming with minimal memory usage
- Works with deeply nested projections

### 3. On-Demand Vector Creation

**Replace pre-allocated vectors with dynamic creation:**
```rust
// Instead of pre-calculating all vector capacities
// Create vectors as needed during streaming
fn create_vector_for_path(&mut self, path: &ProjectionPath) -> Vector {
    // Create only the vectors needed for this specific path
}
```

### 4. Streaming JSON Parser Integration

**Integrate projection with JSON parsing:**
- Use struson's streaming parser with path-based filtering
- Skip parsing of non-projected JSON segments
- Only parse JSON paths that are actually needed
- Maintain streaming efficiency

## Implementation Plan

### Phase 1: Simplify List Vector Management
1. Remove `CumulativeOffsetTracker` system
2. Implement native DuckDB list vector patterns
3. Add proper `set_len()` calls for all list vector levels
4. Test UNNEST operations work correctly

### Phase 2: Path-Based Projection
1. Design `ProjectionPath` system for nested projections
2. Implement JSON path parsing from SQL projection
3. Integrate with struson for path-based JSON filtering
4. Test projection works at all nesting levels

### Phase 3: Memory Optimization
1. Remove pre-allocated vector capacity system
2. Implement dynamic vector creation
3. Ensure true streaming with minimal memory footprint
4. Performance testing and optimization

### Phase 4: Integration Testing
1. Test complex UNNEST queries with projection
2. Verify memory efficiency with large JSON files
3. Ensure compatibility with all DuckDB operations
4. Performance comparison with DuckDB's default JSON reader

## Expected Benefits

1. **Correct UNNEST Support**: All UNNEST operations work properly
2. **True Projection Pushdown**: Works at all nesting levels, not just top level
3. **Memory Efficiency**: Only process and store projected data
4. **Architectural Simplicity**: Remove over-engineered offset tracking
5. **DuckDB Compatibility**: Use native DuckDB patterns throughout
6. **Streaming Performance**: Maintain O(row_size) memory usage

## Migration Strategy

This redesign requires significant architectural changes. Recommend:
1. Implement in parallel branch to maintain current functionality
2. Comprehensive testing of new approach before migration
3. Gradual migration with fallback to current implementation
4. Performance benchmarking throughout transition

The current offset tracking system, while complex, does work for basic cases. The redesign should be thoroughly validated before replacing the existing implementation.
