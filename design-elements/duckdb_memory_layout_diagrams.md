# DuckDB Memory Layout Patterns

## Multi-dimensional Arrays

### `LIST[LIST[NUMBER]]` Memory Layout
- **Outer ListVector**: Contains `{offset, length}` entries pointing to inner arrays
- **Inner ListVector**: Contains `{offset, length}` entries pointing to primitive data
- **Leaf FlatVector**: Contiguous array of primitive values
- **Cumulative Offsets**: Each array's offset is cumulative across all previous elements

### DuckDB API Usage
```rust
// Set list entries with offset and length
list_vector.set_entry(row_idx, offset, length);

// Access child vectors
let struct_vector = list_vector.struct_child(capacity);
let flat_vector = list_vector.child(capacity);
```

## Arrays of Structs

### `LIST[STRUCT]` Memory Layout
- **ListVector**: Contains entries pointing to struct objects
- **StructVector**: Each field stored in separate vector
- **Columnar Storage**: All values for same field stored contiguously
- **Index Alignment**: All field vectors use same indices for same struct

## Nested Combinations

### Complex Nesting Pattern
- **Root ListVector**: Points to struct objects
- **StructVector**: Contains both primitive fields and nested list fields
- **Nested ListVector**: Within struct fields, manages variable-length arrays
- **Leaf FlatVector**: Contains all primitive data in contiguous storage

## Key Implementation Patterns

### Memory Reservation
```rust
// Reserve capacity before data insertion
list_vector.reserve(total_capacity);
```

### Offset Management
- Each list entry contains `{offset, length}` pair
- Offsets are cumulative across all previous elements
- Child vector stores all primitive data contiguously

## Vector Type Selection

### DuckDB API Patterns
- `list_vector.child()` → FlatVector (primitives)
- `list_vector.struct_child()` → StructVector (objects)
- `list_vector.list_child()` → ListVector (nested arrays)

### Memory Efficiency
- Columnar storage for each data type
- Minimal overhead from offset/length pairs
- Zero-copy access through direct pointer arithmetic
