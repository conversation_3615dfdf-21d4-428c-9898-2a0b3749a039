use duckdb::core::{<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON>, FlatVector, In<PERSON><PERSON>, LogicalTypeId};
use duckdb::vtab::{BindInfo, InitInfo, VTab};
use duckdb::{ffi, Connection};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use std::collections::HashMap;
use std::fs::File;
use std::io::BufReader;

use struson::reader::{<PERSON>sonReader, JsonStreamReader};

/// Represents a path to a specific vector in the nested structure
#[derive(Debug, <PERSON>lone, Hash, Eq, PartialEq)]
pub struct VectorPath {
    pub path_components: Vec<PathComponent>,
}

#[derive(Debug, <PERSON>lone, Hash, Eq, PartialEq)]
pub enum PathComponent {
    Root,
    ArrayElement,
    ObjectField(String),
}

/// Recursive JSON type representation
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum InferredJsonType {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    String,
    Array {
        element_type: Box<InferredJsonType>,
        max_length: usize,
        total_instances: usize,
    },
    Object {
        fields: Vec<(String, InferredJsonType)>,
        total_instances: usize,
    },
}

/// Schema inference result
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
}

impl InferredSchema {
    pub fn summary(&self) -> String {
        format!("Schema: {:?}", self.root_type)
    }
}

/// Vector capacities (simplified for now)
#[derive(Debug, Clone)]
pub struct VectorCapacities {
    pub capacities: HashMap<VectorPath, usize>,
}

/// Schema inference config (simplified for now)
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub enable_debug_output: bool,
}

/// Real schema inference implementation
pub fn infer_schema_from_file(file_path: &str, config: SchemaInferenceConfig) -> Result<(InferredSchema, VectorCapacities), Box<dyn std::error::Error>> {
    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Starting analysis of file: {}", file_path);
    }

    // Open and parse the JSON file
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    // Infer the root type by examining the JSON structure
    let root_type = infer_json_type(&mut json_reader, &config)?;

    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Detected root type: {:?}", root_type);
    }

    let schema = InferredSchema { root_type };
    let capacities = VectorCapacities { capacities: HashMap::new() };

    Ok((schema, capacities))
}

/// Infer the JSON type from the stream
fn infer_json_type(json_reader: &mut JsonStreamReader<BufReader<File>>, config: &SchemaInferenceConfig) -> Result<InferredJsonType, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found array at root");
            }

            json_reader.begin_array()?;

            if !json_reader.has_next()? {
                // Empty array
                json_reader.end_array()?;
                return Ok(InferredJsonType::Array {
                    element_type: Box::new(InferredJsonType::Null),
                    max_length: 0,
                    total_instances: 0,
                });
            }

            // Infer type from first element
            let element_type = infer_json_type(json_reader, config)?;

            // Count remaining elements (simplified - just count a few)
            let mut count = 1;
            while json_reader.has_next()? && count < 10 {
                json_reader.skip_value()?;
                count += 1;
            }

            json_reader.end_array()?;

            Ok(InferredJsonType::Array {
                element_type: Box::new(element_type),
                max_length: count,
                total_instances: 1,
            })
        },

        struson::reader::ValueType::Object => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found object at root");
            }

            json_reader.begin_object()?;

            let mut fields = Vec::new();
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_type = infer_json_type(json_reader, config)?;
                fields.push((field_name, field_type));

                if config.enable_debug_output {
                    eprintln!("SCHEMA INFERENCE: Found field: {}", fields.last().unwrap().0);
                }
            }

            json_reader.end_object()?;

            Ok(InferredJsonType::Object {
                fields,
                total_instances: 1,
            })
        },

        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(InferredJsonType::String)
        },

        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(InferredJsonType::Number)
        },

        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(InferredJsonType::Boolean)
        },

        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(InferredJsonType::Null)
        },
    }
}

/// Create a JsonStreamReader with settings optimized for DuckDB JSON extension
fn create_json_reader(reader: BufReader<File>) -> JsonStreamReader<BufReader<File>> {
    let settings = struson::reader::ReaderSettings {
        allow_comments: false,
        allow_trailing_comma: false,
        restrict_number_values: false,
        allow_multiple_top_level: false,
        max_nesting_depth: Some(128),
        track_path: false,
    };
    JsonStreamReader::new_custom(reader, settings)
}

/// Create proper DuckDB LogicalTypeHandle from InferredJsonType
fn create_duckdb_type(json_type: &InferredJsonType) -> Result<duckdb::core::LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        InferredJsonType::Null => Ok(LogicalTypeId::Varchar.into()),
        InferredJsonType::Boolean => Ok(LogicalTypeId::Boolean.into()),
        InferredJsonType::Number => Ok(LogicalTypeId::Double.into()),
        InferredJsonType::String => Ok(LogicalTypeId::Varchar.into()),
        InferredJsonType::Array { element_type, .. } => {
            // Create proper LIST type with element type
            let element_logical_type = create_duckdb_type(element_type)?;
            Ok(duckdb::core::LogicalTypeHandle::list(&element_logical_type))
        },
        InferredJsonType::Object { fields, .. } => {
            // Create proper STRUCT type with field types
            let mut struct_fields = Vec::new();
            for (field_name, field_type) in fields {
                let field_logical_type = create_duckdb_type(field_type)?;
                struct_fields.push((field_name.as_str(), field_logical_type));
            }
            Ok(duckdb::core::LogicalTypeHandle::struct_type(&struct_fields))
        },
    }
}

/// Configuration for data loading behavior
#[derive(Debug, Clone)]
pub struct DataLoaderConfig {
    pub enable_debug_output: bool,
    pub enable_progress_reporting: bool,
    pub batch_size: usize,
    pub validate_offsets: bool,
}

impl Default for DataLoaderConfig {
    fn default() -> Self {
        Self {
            enable_debug_output: false,
            enable_progress_reporting: false,
            batch_size: 2048,
            validate_offsets: true,
        }
    }
}

/// Error types for data loading
#[derive(Debug)]
pub enum DataLoaderError {
    JsonParsingError(String),
    VectorError(String),
}

impl std::fmt::Display for DataLoaderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DataLoaderError::JsonParsingError(msg) => write!(f, "JSON parsing error: {}", msg),
            DataLoaderError::VectorError(msg) => write!(f, "Vector error: {}", msg),
        }
    }
}

impl std::error::Error for DataLoaderError {}

/// Parsed value types for intermediate storage during loading
#[derive(Debug, Clone)]
pub enum ParsedValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<ParsedValue>),
    Object(HashMap<String, ParsedValue>),
}

/// FRESH START: Native DuckDB streaming data loader
/// This implementation uses DuckDB's native list vector patterns without global offset tracking
pub struct StreamingDataLoader {
    schema: InferredSchema,
    config: DataLoaderConfig,
    projection_mapping: HashMap<usize, usize>, // Maps schema column index to output vector index
}

impl StreamingDataLoader {
    pub fn new(schema: InferredSchema, _vector_capacities: VectorCapacities, config: DataLoaderConfig, projection_mapping: HashMap<usize, usize>) -> Self {
        Self {
            schema,
            config,
            projection_mapping,
        }
    }

    /// NEW IMPLEMENTATION: Load data using native DuckDB list vector patterns
    /// This replaces all the old offset tracking methods with a clean approach
    pub fn load_data_from_file(
        &mut self,
        file_path: &str,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("FRESH START: Loading data with native DuckDB approach for file: {}", file_path);
            eprintln!("Schema: {}", self.schema.summary());
        }

        // Open file and create JSON reader
        let file = File::open(file_path)
            .map_err(|e| DataLoaderError::JsonParsingError(format!("Failed to open file: {}", e)))?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = create_json_reader(buf_reader);

        // Load data based on root schema type using native DuckDB patterns
        let rows_processed = match self.schema.root_type.clone() {
            InferredJsonType::Array { element_type, .. } => {
                self.load_array_native(&mut json_reader, output, column_index, &element_type)?
            },
            InferredJsonType::Object { .. } => {
                self.load_object_native(&mut json_reader, output, column_index)?
            },
            _ => {
                self.load_primitive_native(&mut json_reader, output, column_index)?
            }
        };

        if self.config.enable_debug_output {
            eprintln!("FRESH START: Completed data loading. Rows processed: {}", rows_processed);
        }

        Ok(rows_processed)
    }

    /// Load array data using native DuckDB list vector patterns
    fn load_array_native(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
        element_type: &InferredJsonType,
    ) -> Result<usize, DataLoaderError> {
        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // For UNNEST operations, we need to detect nested arrays
        let is_unnest_case = matches!(element_type, InferredJsonType::Array { .. });

        let result = if is_unnest_case {
            // UNNEST case: nested arrays need special handling
            self.load_unnest_arrays_native(json_reader, output, column_index, element_type)
        } else {
            // Normal case: array of objects/primitives flattened into rows
            self.load_flat_array_native(json_reader, output, column_index, element_type)
        };

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        result
    }

    /// Load UNNEST arrays using native DuckDB patterns
    fn load_unnest_arrays_native(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
        _element_type: &InferredJsonType,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("NATIVE UNNEST: Processing nested arrays for UNNEST operation");
        }

        // CORE IMPLEMENTATION: Native DuckDB UNNEST using list vector patterns
        // This is where we'll implement the proper UNNEST logic

        // Step 1: Collect all nested arrays from the JSON
        let mut all_inner_arrays = Vec::new();
        let mut json_object_count = 0;

        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            // Parse each JSON object
            json_reader.begin_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                let field_name = json_reader.next_name()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

                if field_name == "matrix" { // TODO: Make this dynamic based on schema
                    // Parse the nested array
                    let nested_array = self.parse_nested_array(json_reader)?;
                    all_inner_arrays.extend(nested_array);
                } else {
                    json_reader.skip_value()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                }
            }

            json_reader.end_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            json_object_count += 1;
        }

        // Step 2: Create list vector using native DuckDB patterns
        let mut list_vector = output.list_vector(column_index);

        // Step 3: Set up list entries for each inner array (UNNEST pattern)
        let total_inner_arrays = all_inner_arrays.len();
        let mut current_offset = 0;

        for (row_index, inner_array) in all_inner_arrays.iter().enumerate() {
            list_vector.set_entry(row_index, current_offset, inner_array.len());
            current_offset += inner_array.len();

            if self.config.enable_debug_output {
                eprintln!("NATIVE UNNEST: Set list entry for row {} with offset {} and length {}",
                         row_index, current_offset - inner_array.len(), inner_array.len());
            }
        }

        // Step 4: Set total child vector length
        list_vector.set_len(current_offset);

        // Step 5: Fill child vector with primitive data
        let mut child_vector = list_vector.child(current_offset);
        let mut child_position = 0;

        for inner_array in &all_inner_arrays {
            for value in inner_array {
                self.insert_primitive_value(&mut child_vector, child_position, value)?;
                child_position += 1;
            }
        }

        if self.config.enable_debug_output {
            eprintln!("NATIVE UNNEST: Processed {} JSON objects into {} output rows with {} total elements",
                     json_object_count, total_inner_arrays, current_offset);
        }

        Ok(total_inner_arrays)
    }

    /// Parse nested array structure
    fn parse_nested_array(&mut self, json_reader: &mut JsonStreamReader<BufReader<File>>) -> Result<Vec<Vec<f64>>, DataLoaderError> {
        let mut result = Vec::new();

        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            let mut inner_array = Vec::new();

            json_reader.begin_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                let value = json_reader.next_number_as_str()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                    .parse::<f64>()
                    .map_err(|e| DataLoaderError::JsonParsingError(format!("Invalid number: {}", e)))?;

                inner_array.push(value);
            }

            json_reader.end_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            result.push(inner_array);
        }

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        Ok(result)
    }

    /// Insert primitive value into flat vector
    fn insert_primitive_value(&mut self, _flat_vector: &mut FlatVector, position: usize, value: &f64) -> Result<(), DataLoaderError> {
        // TODO: Fix FlatVector usage - for now just log
        if self.config.enable_debug_output {
            eprintln!("NATIVE: Would insert value {} at position {}", value, position);
        }

        Ok(())
    }

    /// Load flat array (array of objects/primitives) using native DuckDB patterns
    fn load_flat_array_native(
        &mut self,
        _json_reader: &mut JsonStreamReader<BufReader<File>>,
        _output: &DataChunkHandle,
        _column_index: usize,
        _element_type: &InferredJsonType,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("NATIVE FLAT: Processing flat array (each element becomes a row)");
        }

        // TODO: Implement native flat array processing
        Ok(0)
    }

    /// Load object data using native DuckDB patterns
    fn load_object_native(
        &mut self,
        _json_reader: &mut JsonStreamReader<BufReader<File>>,
        _output: &DataChunkHandle,
        _column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("NATIVE OBJECT: Processing single object");
        }

        // TODO: Implement native object processing
        Ok(0)
    }

    /// Load primitive data using native DuckDB patterns
    fn load_primitive_native(
        &mut self,
        _json_reader: &mut JsonStreamReader<BufReader<File>>,
        _output: &DataChunkHandle,
        _column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("NATIVE PRIMITIVE: Processing single primitive value");
        }

        // TODO: Implement native primitive processing
        Ok(0)
    }
}

// ============================================================================
// DuckDB Table Function Implementation
// ============================================================================

#[repr(C)]
pub struct JsonReaderBindData {
    file_path: String,
    schema: InferredSchema,
}

#[repr(C)]
pub struct JsonReaderInitData {
    finished: std::sync::atomic::AtomicBool,
}

struct JsonReaderVTab;

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> duckdb::Result<Self::BindData, Box<dyn std::error::Error>> {
        // Check if we have parameters
        if bind.get_parameter_count() == 0 {
            return Err("streaming_json_reader requires a file path parameter".into());
        }

        // Get file path from first parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference
        let config = SchemaInferenceConfig {
            enable_debug_output: false,
        };

        let (schema, _capacities) = infer_schema_from_file(&file_path, config)
            .map_err(|e| format!("Schema inference failed: {}", e))?;

        // Add columns based on inferred schema
        match &schema.root_type {
            InferredJsonType::Array { element_type, .. } => {
                match element_type.as_ref() {
                    InferredJsonType::Object { fields, .. } => {
                        // Array of objects - each field becomes a column
                        if fields.is_empty() {
                            // Array of empty objects - add fallback column
                            bind.add_result_column("json", LogicalTypeId::Varchar.into());
                        } else {
                            for (field_name, field_type) in fields {
                                let logical_type = create_duckdb_type(field_type)?;
                                bind.add_result_column(field_name, logical_type);
                            }
                        }
                    }
                    _ => {
                        // Array of primitives - single column with correct type
                        let logical_type = create_duckdb_type(element_type)?;
                        bind.add_result_column("value", logical_type);
                    }
                }
            }
            InferredJsonType::Object { fields, .. } => {
                // Single object - each field becomes a column
                if fields.is_empty() {
                    // Empty object - add fallback column
                    bind.add_result_column("json", LogicalTypeId::Varchar.into());
                } else {
                    for (field_name, field_type) in fields {
                        let logical_type = create_duckdb_type(field_type)?;
                        bind.add_result_column(field_name, logical_type);
                    }
                }
            }
            _ => {
                // Single primitive - single column with correct type
                let logical_type = create_duckdb_type(&schema.root_type)?;
                bind.add_result_column("value", logical_type);
            }
        }

        Ok(JsonReaderBindData {
            file_path,
            schema,
        })
    }

    fn init(_init: &InitInfo) -> duckdb::Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(JsonReaderInitData {
            finished: std::sync::atomic::AtomicBool::new(false),
        })
    }

    fn func(info: &duckdb::vtab::TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> duckdb::Result<(), Box<dyn std::error::Error>> {
        let init_data = info.get_init_data();
        let bind_data = unsafe { &*(info.get_bind_data() as *const JsonReaderBindData) };

        // Check if already finished
        if (*init_data).finished.load(std::sync::atomic::Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // Open and parse the JSON file
        let file = File::open(&bind_data.file_path)
            .map_err(|e| format!("Failed to open file '{}': {}", bind_data.file_path, e))?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = create_json_reader(buf_reader);

        // Load data based on schema type
        let rows_loaded = match &bind_data.schema.root_type {
            InferredJsonType::Object { fields, .. } => {
                // Single object - load as one row with fields as columns
                load_object_as_row(&mut json_reader, output, fields)?
            },
            InferredJsonType::Array { element_type, .. } => {
                // Array - each element becomes a row
                load_array_as_rows(&mut json_reader, output, element_type)?
            },
            _ => {
                // Single primitive - load as one row
                load_primitive_as_row(&mut json_reader, output)?
            }
        };

        output.set_len(rows_loaded);
        (*init_data).finished.store(true, std::sync::atomic::Ordering::Relaxed);

        Ok(())
    }

    fn parameters() -> Option<Vec<duckdb::core::LogicalTypeHandle>> {
        Some(vec![duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

// ============================================================================
// Data Loading Helper Functions
// ============================================================================

/// Load JSON object as a single row with fields as columns
fn load_object_as_row(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    fields: &[(String, InferredJsonType)],
) -> Result<usize, Box<dyn std::error::Error>> {
    json_reader.begin_object()?;

    // Parse all field values
    let mut field_values = HashMap::new();
    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();

        // Parse value based on type
        let value = match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                None
            },
            struson::reader::ValueType::Boolean => {
                Some(json_reader.next_bool()?.to_string())
            },
            struson::reader::ValueType::Number => {
                Some(json_reader.next_number_as_str()?.to_string())
            },
            struson::reader::ValueType::String => {
                Some(json_reader.next_string()?)
            },
            _ => {
                // For complex types, skip for now
                json_reader.skip_value()?;
                None
            }
        };

        field_values.insert(field_name, value);
    }

    json_reader.end_object()?;

    // Handle empty objects with fallback column
    if fields.is_empty() {
        let flat_vector = output.flat_vector(0);
        flat_vector.insert(0, "{}");
        return Ok(1);
    }

    // Insert values into output vectors
    for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
        let mut flat_vector = output.flat_vector(col_idx);

        if let Some(value) = field_values.get(field_name) {
            if let Some(val_str) = value {
                match field_type {
                    InferredJsonType::Number => {
                        if let Ok(num) = val_str.parse::<f64>() {
                            flat_vector.as_mut_slice::<f64>()[0] = num;
                        } else {
                            flat_vector.set_null(0);
                        }
                    },
                    InferredJsonType::Boolean => {
                        if let Ok(b) = val_str.parse::<bool>() {
                            flat_vector.as_mut_slice::<bool>()[0] = b;
                        } else {
                            flat_vector.set_null(0);
                        }
                    },
                    _ => {
                        flat_vector.insert(0, val_str.as_str());
                    }
                }
            } else {
                flat_vector.set_null(0);
            }
        } else {
            flat_vector.set_null(0);
        }
    }

    Ok(1) // One row loaded
}

/// Load JSON array as multiple rows
fn load_array_as_rows(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    element_type: &InferredJsonType,
) -> Result<usize, Box<dyn std::error::Error>> {
    json_reader.begin_array()?;

    let mut row_count = 0;

    match element_type {
        InferredJsonType::Object { fields, .. } => {
            // Array of objects - each object becomes a row with fields as columns
            while json_reader.has_next()? {
                load_object_as_row_from_array(json_reader, output, fields, row_count)?;
                row_count += 1;
            }
        },
        _ => {
            // Array of primitives - each element becomes a row in single column
            while json_reader.has_next()? {
                let mut flat_vector = output.flat_vector(0);
                load_primitive_value_into_vector(json_reader, &mut flat_vector, row_count, element_type)?;
                row_count += 1;
            }
        }
    }

    json_reader.end_array()?;
    Ok(row_count)
}

/// Load single primitive as one row
fn load_primitive_as_row(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    let mut flat_vector = output.flat_vector(0);

    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            flat_vector.set_null(0);
        },
        struson::reader::ValueType::Boolean => {
            let value = json_reader.next_bool()?;
            flat_vector.as_mut_slice::<bool>()[0] = value;
        },
        struson::reader::ValueType::Number => {
            let value_str = json_reader.next_number_as_str()?;
            if let Ok(num) = value_str.parse::<f64>() {
                flat_vector.as_mut_slice::<f64>()[0] = num;
            } else {
                flat_vector.set_null(0);
            }
        },
        struson::reader::ValueType::String => {
            let value = json_reader.next_string()?;
            flat_vector.insert(0, value.as_str());
        },
        _ => {
            // For complex types, convert to string representation
            json_reader.skip_value()?;
            flat_vector.insert(0, "{}");
        }
    }

    Ok(1)
}

/// Load object from array as a row with fields as columns
fn load_object_as_row_from_array(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    fields: &[(String, InferredJsonType)],
    row_index: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    json_reader.begin_object()?;

    // Parse all field values
    let mut field_values = HashMap::new();
    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();

        // Parse value based on type
        let value = match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                None
            },
            struson::reader::ValueType::Boolean => {
                Some(json_reader.next_bool()?.to_string())
            },
            struson::reader::ValueType::Number => {
                Some(json_reader.next_number_as_str()?.to_string())
            },
            struson::reader::ValueType::String => {
                Some(json_reader.next_string()?)
            },
            _ => {
                // For complex types, skip for now
                json_reader.skip_value()?;
                None
            }
        };

        field_values.insert(field_name, value);
    }

    json_reader.end_object()?;

    // Insert values into output vectors
    for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
        let mut flat_vector = output.flat_vector(col_idx);

        if let Some(value) = field_values.get(field_name) {
            if let Some(val_str) = value {
                match field_type {
                    InferredJsonType::Number => {
                        if let Ok(num) = val_str.parse::<f64>() {
                            flat_vector.as_mut_slice::<f64>()[row_index] = num;
                        } else {
                            flat_vector.set_null(row_index);
                        }
                    },
                    InferredJsonType::Boolean => {
                        if let Ok(b) = val_str.parse::<bool>() {
                            flat_vector.as_mut_slice::<bool>()[row_index] = b;
                        } else {
                            flat_vector.set_null(row_index);
                        }
                    },
                    _ => {
                        flat_vector.insert(row_index, val_str.as_str());
                    }
                }
            } else {
                flat_vector.set_null(row_index);
            }
        } else {
            flat_vector.set_null(row_index);
        }
    }

    Ok(())
}

/// Load primitive value into vector at specific row
fn load_primitive_value_into_vector(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    flat_vector: &mut FlatVector,
    row_index: usize,
    value_type: &InferredJsonType,
) -> Result<(), Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            flat_vector.set_null(row_index);
        },
        struson::reader::ValueType::Boolean => {
            let value = json_reader.next_bool()?;
            flat_vector.as_mut_slice::<bool>()[row_index] = value;
        },
        struson::reader::ValueType::Number => {
            let value_str = json_reader.next_number_as_str()?;
            match value_type {
                InferredJsonType::Number => {
                    if let Ok(num) = value_str.parse::<f64>() {
                        flat_vector.as_mut_slice::<f64>()[row_index] = num;
                    } else {
                        flat_vector.set_null(row_index);
                    }
                },
                _ => {
                    flat_vector.insert(row_index, value_str);
                }
            }
        },
        struson::reader::ValueType::String => {
            let value = json_reader.next_string()?;
            flat_vector.insert(row_index, value.as_str());
        },
        _ => {
            // For complex types, convert to string representation
            json_reader.skip_value()?;
            flat_vector.insert(row_index, "{}");
        }
    }

    Ok(())
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn streaming_json_reader_init(db: duckdb::Connection) -> duckdb::Result<(), Box<dyn std::error::Error>> {
    db.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .map_err(|e| format!("Failed to register table function: {}", e))?;

    eprintln!("Extension loaded successfully - table function registered");
    Ok(())
}

// ============================================================================
// Unit Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_schema_inference_with_temp_file() {
        use std::io::Write;

        let config = SchemaInferenceConfig {
            enable_debug_output: false,
        };

        // Create a temporary JSON file
        let mut temp_file = tempfile::NamedTempFile::new().unwrap();
        let json_data = r#"{"name": "test", "value": 42}"#;
        temp_file.write_all(json_data.as_bytes()).unwrap();
        temp_file.flush().unwrap();

        // Test schema inference on real file
        let result = infer_schema_from_file(temp_file.path().to_str().unwrap(), config);
        assert!(result.is_ok());

        let (schema, _capacities) = result.unwrap();
        // Should detect an object with two fields
        match schema.root_type {
            InferredJsonType::Object { fields, .. } => {
                assert_eq!(fields.len(), 2);
                assert_eq!(fields[0].0, "name");
                assert_eq!(fields[1].0, "value");
            }
            _ => panic!("Expected object type for JSON object"),
        }
    }

    #[test]
    fn test_data_loader_creation() {
        let schema = InferredSchema {
            root_type: InferredJsonType::Array {
                element_type: Box::new(InferredJsonType::Number),
                max_length: 10,
                total_instances: 1,
            },
        };

        let capacities = VectorCapacities { capacities: HashMap::new() };
        let config = DataLoaderConfig {
            enable_debug_output: false,
            batch_size: 1000,
            enable_progress_reporting: false,
            validate_offsets: false,
        };
        let projection_mapping = HashMap::new();

        let loader = StreamingDataLoader::new(schema, capacities, config, projection_mapping);

        // Should create successfully
        assert_eq!(loader.config.enable_debug_output, false);
    }

    #[test]
    fn test_json_reader_creation() {
        // Test that we can create a JSON reader with proper settings
        let settings = struson::reader::ReaderSettings {
            allow_comments: false,
            allow_trailing_comma: false,
            restrict_number_values: false,
            allow_multiple_top_level: false,
            max_nesting_depth: Some(128),
            track_path: false,
        };

        // Just test that the settings are valid
        assert_eq!(settings.allow_comments, false);
        assert_eq!(settings.max_nesting_depth, Some(128));
    }
}